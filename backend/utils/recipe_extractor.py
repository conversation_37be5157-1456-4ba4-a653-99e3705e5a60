import json
import re
import requests
from typing import Dict, List, Optional, Tuple
from models.schemas import RecipeCreate, IngredientBase, InstructionStep


class RecipeExtractor:
    """Extract recipe information from video transcripts using AI"""
    
    def __init__(self):
        self.ollama_endpoints = [
            'http://ollama:11434/api/generate',  # Docker service
            'http://localhost:11434/api/generate'  # Local fallback
        ]
    
    async def extract_recipe(self, transcript: str, video_title: str = "", video_id: int = None) -> Optional[RecipeCreate]:
        """Extract recipe from transcript if it's a cooking video"""
        try:
            # First, check if this is actually a recipe/cooking video
            if not self._is_cooking_video(transcript, video_title):
                return None
            
            # Extract recipe using Ollama
            recipe_data = await self._extract_recipe_with_ollama(transcript, video_title)
            
            if recipe_data and video_id:
                # Convert to RecipeCreate schema
                return self._convert_to_recipe_create(recipe_data, video_id)
            
            return None
            
        except Exception as e:
            print(f"Recipe extraction failed: {e}")
            return None
    
    def _is_cooking_video(self, transcript: str, title: str = "") -> bool:
        """Determine if this is a cooking/recipe video"""
        text = f"{title} {transcript}".lower()
        
        # Cooking keywords in multiple languages
        cooking_keywords = [
            # English
            'recipe', 'cook', 'bake', 'fry', 'boil', 'ingredients', 'kitchen', 'oven',
            'stir', 'mix', 'chop', 'slice', 'dice', 'season', 'taste', 'serve',
            'tablespoon', 'teaspoon', 'cup', 'pound', 'ounce', 'gram', 'liter',
            'minutes', 'degrees', 'heat', 'oil', 'salt', 'pepper', 'garlic',
            
            # Spanish
            'receta', 'cocinar', 'hornear', 'freír', 'hervir', 'ingredientes', 'cocina', 'horno',
            'mezclar', 'revolver', 'picar', 'cortar', 'sazonar', 'probar', 'servir',
            'cucharada', 'cucharadita', 'taza', 'gramo', 'litro', 'minutos', 'grados',
            'aceite', 'sal', 'pimienta', 'ajo', 'cebolla', 'tomate', 'pollo', 'carne',
            'pasta', 'arroz', 'verduras', 'queso', 'huevo', 'leche', 'azúcar', 'harina'
        ]
        
        # Count cooking-related words
        cooking_word_count = sum(1 for keyword in cooking_keywords if keyword in text)
        
        # Consider it a cooking video if it has enough cooking keywords
        return cooking_word_count >= 3
    
    async def _extract_recipe_with_ollama(self, transcript: str, title: str = "") -> Optional[Dict]:
        """Extract recipe using Ollama LLM"""
        try:
            prompt = f'''Eres un chef experto y analizador de recetas. Analiza esta transcripción de video de cocina y extrae una receta completa con ingredientes e instrucciones paso a paso.

TÍTULO DEL VIDEO: "{title}"
TRANSCRIPCIÓN: "{transcript}"

TAREA: Extrae la información de la receta y devuélvela como un objeto JSON con esta estructura exacta:

IMPORTANTE: TODA la respuesta debe estar en ESPAÑOL. Traduce cualquier contenido al español.

{{
  "title": "Nombre de la receta en español (si se menciona, o crea un nombre descriptivo)",
  "description": "Breve descripción del plato en español",
  "ingredients": [
    {{
      "name": "nombre del ingrediente en español",
      "amount": "cantidad con unidad (ej., '2 tazas', '1 cucharada', '500g')",
      "unit": "solo la unidad en español (ej., 'tazas', 'cucharadas', 'g')",
      "notes": "notas de preparación en español (ej., 'finamente picado', 'a temperatura ambiente')"
    }}
  ],
  "instructions": [
    {{
      "step_number": 1,
      "instruction": "Instrucción detallada del paso en español",
      "time": "tiempo de cocción si se menciona (ej., '5 minutos', 'hasta dorar')",
      "temperature": "temperatura si se menciona (ej., '180°C', 'fuego medio')"
    }}
  ],
  "prep_time": "tiempo de preparación en español (ej., '15 minutos')",
  "cook_time": "tiempo de cocción en español (ej., '30 minutos')",
  "total_time": "tiempo total en español (ej., '45 minutos')",
  "servings": "número de porciones en español (ej., '4 porciones', '6 raciones')",
  "difficulty": "Fácil, Medio, o Difícil",
  "cuisine_type": "tipo de cocina en español (ej., 'Italiana', 'Mexicana', 'Asiática')",
  "confidence": 0.85
}}

DIRECTRICES IMPORTANTES:
1. Extrae TODOS los ingredientes mencionados con sus cantidades
2. Divide las instrucciones en pasos claros y numerados
3. TRADUCE TODO AL ESPAÑOL (ingredientes, instrucciones, descripciones)
4. Incluye tiempos de cocción y temperaturas cuando se mencionen
5. Si las cantidades no están claras, usa "al gusto" o "según sea necesario"
6. Establece la confianza entre 0.0-1.0 basándote en qué tan clara es la receta
7. Si no se encuentra una receta clara, devuelve {{"confidence": 0.0}}

Respuesta JSON:'''

            print(f"Extracting recipe from transcript: {transcript[:100]}...")
            
            # Try Ollama endpoints
            for endpoint in self.ollama_endpoints:
                try:
                    print(f"Trying Ollama endpoint: {endpoint}")
                    response = requests.post(
                        endpoint,
                        json={
                            'model': 'llama3.2:3b',
                            'prompt': prompt,
                            'stream': False,
                            'options': {
                                'temperature': 0.3,  # Low temperature for consistent extraction
                                'top_p': 0.9,
                                'num_predict': 1000,  # Allow longer responses for recipes
                                'stop': ['\n\nHuman:', '\n\nAssistant:']
                            }
                        },
                        timeout=120  # Longer timeout for recipe extraction
                    )
                    
                    if response.status_code == 200:
                        print(f"Successfully connected to Ollama at {endpoint}")
                        result = response.json()
                        generated_text = result.get('response', '').strip()
                        print(f"Ollama recipe response: {generated_text[:200]}...")
                        
                        # Parse the JSON response
                        recipe_data = self._parse_recipe_response(generated_text)
                        
                        if recipe_data and recipe_data.get('confidence', 0) > 0.5:
                            print(f"Recipe extracted with confidence: {recipe_data.get('confidence')}")
                            return recipe_data
                        else:
                            print("Low confidence recipe extraction, skipping")
                            return None
                            
                except requests.exceptions.RequestException as e:
                    print(f"Failed to connect to {endpoint}: {e}")
                    continue
            
            print("All Ollama endpoints failed")
            return None
            
        except Exception as e:
            print(f"Recipe extraction with Ollama failed: {e}")
            return None
    
    def _parse_recipe_response(self, response_text: str) -> Optional[Dict]:
        """Parse Ollama response and extract recipe JSON"""
        try:
            # Try to find JSON in the response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                recipe_data = json.loads(json_str)
                return recipe_data
            else:
                print("No JSON found in recipe response")
                return None
                
        except json.JSONDecodeError as e:
            print(f"Failed to parse recipe JSON: {e}")
            return None
    
    def _convert_to_recipe_create(self, recipe_data: Dict, video_id: int) -> RecipeCreate:
        """Convert extracted recipe data to RecipeCreate schema"""
        try:
            # Convert ingredients
            ingredients = []
            for ing_data in recipe_data.get('ingredients', []):
                ingredient = IngredientBase(
                    name=ing_data.get('name', ''),
                    amount=ing_data.get('amount'),
                    unit=ing_data.get('unit'),
                    notes=ing_data.get('notes')
                )
                ingredients.append(ingredient)
            
            # Convert instructions
            instructions = []
            for inst_data in recipe_data.get('instructions', []):
                instruction = InstructionStep(
                    step_number=inst_data.get('step_number', len(instructions) + 1),
                    instruction=inst_data.get('instruction', ''),
                    time=inst_data.get('time'),
                    temperature=inst_data.get('temperature')
                )
                instructions.append(instruction)
            
            # Create RecipeCreate object
            recipe_create = RecipeCreate(
                video_id=video_id,
                title=recipe_data.get('title'),
                description=recipe_data.get('description'),
                ingredients=ingredients,
                instructions=instructions,
                prep_time=recipe_data.get('prep_time'),
                cook_time=recipe_data.get('cook_time'),
                total_time=recipe_data.get('total_time'),
                servings=recipe_data.get('servings'),
                difficulty=recipe_data.get('difficulty'),
                cuisine_type=recipe_data.get('cuisine_type'),
                extraction_confidence=recipe_data.get('confidence', 0.0)
            )
            
            return recipe_create
            
        except Exception as e:
            print(f"Failed to convert recipe data: {e}")
            return None
