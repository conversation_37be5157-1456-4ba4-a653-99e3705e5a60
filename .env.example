# tagTok Environment Configuration Example
# Copy this file to .env and modify as needed

# Database Configuration
DATABASE_URL=sqlite:///db/tagTok.db

# Directory Paths
VIDEOS_DIR=/app/videos
TRANSCRIPTS_DIR=/app/transcripts

# API Configuration
REACT_APP_API_URL=http://localhost:8090

# AI Model Configuration
WHISPER_MODEL_SIZE=base

# Development Configuration
PYTHONPATH=/app
CHOKIDAR_USEPOLLING=true

# Docker Configuration
COMPOSE_PROJECT_NAME=tagtok

# Port Configuration (change if ports are in use)
NGINX_PORT=8790
BACKEND_PORT=8090
FRONTEND_PORT=3001
OLLAMA_PORT=11435
