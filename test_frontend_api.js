#!/usr/bin/env node

const http = require('http');

// Test the API endpoints that the frontend is trying to access
const baseUrl = 'http://**************:8090';

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const request = http.get(url, (response) => {
      let data = '';
      response.on('data', (chunk) => {
        data += chunk;
      });
      response.on('end', () => {
        resolve({ status: response.statusCode, data: data });
      });
    });
    
    request.on('error', (error) => {
      reject(error);
    });
    
    request.setTimeout(10000, () => {
      request.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

async function testEndpoints() {
  console.log('🧪 Testing Frontend API Endpoints\n');
  
  const endpoints = [
    { name: 'Health Check', url: `${baseUrl}/health` },
    { name: 'Videos List', url: `${baseUrl}/videos/?skip=0&limit=5` },
    { name: 'Video Detail (68)', url: `${baseUrl}/videos/68` },
    { name: 'Tags Cloud', url: `${baseUrl}/tags/cloud/data` },
    { name: 'Tags List', url: `${baseUrl}/tags/?skip=0&limit=10` },
    { name: 'Recipes List', url: `${baseUrl}/recipes/?limit=5` },
    { name: 'Recipes with Videos', url: `${baseUrl}/recipes/with-videos?limit=5` },
  ];
  
  for (const endpoint of endpoints) {
    try {
      console.log(`Testing ${endpoint.name}...`);
      const response = await makeRequest(endpoint.url);
      
      if (response.status === 200) {
        console.log(`✅ ${endpoint.name}: OK (${response.status})`);
        
        // Show sample data for some endpoints
        if (endpoint.name === 'Video Detail (68)') {
          try {
            const data = JSON.parse(response.data);
            console.log(`   Video Title: ${data.title}`);
            console.log(`   Has Recipe: ${!!data.recipe}`);
            if (data.recipe) {
              console.log(`   Recipe Title: ${data.recipe.title}`);
            }
          } catch (e) {
            console.log(`   (Could not parse JSON response)`);
          }
        }
        
        if (endpoint.name === 'Recipes with Videos') {
          try {
            const data = JSON.parse(response.data);
            console.log(`   Found ${data.length} recipes with video info`);
            if (data.length > 0) {
              console.log(`   Sample: ${data[0].title} (Video ${data[0].video_id})`);
            }
          } catch (e) {
            console.log(`   (Could not parse JSON response)`);
          }
        }
        
      } else {
        console.log(`❌ ${endpoint.name}: Failed (${response.status})`);
      }
    } catch (error) {
      console.log(`❌ ${endpoint.name}: Error - ${error.message}`);
    }
    console.log('');
  }
  
  console.log('🎯 Summary:');
  console.log('- Frontend should now connect to backend without /api prefix');
  console.log('- All endpoints should be accessible at http://**************:8090');
  console.log('- Try refreshing the frontend at http://**************:3001/');
  console.log('- Test the Recetas page and video pages');
}

testEndpoints().catch(console.error);
