#!/usr/bin/env node

const http = require('http');

const baseUrl = 'http://**************:3000';
const apiUrl = 'http://localhost:8090';

// Helper function to make HTTP requests
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const request = http.get(url, (response) => {
      let data = '';
      response.on('data', (chunk) => {
        data += chunk;
      });
      response.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: response.statusCode, data: jsonData });
        } catch (error) {
          resolve({ status: response.statusCode, data: data });
        }
      });
    });
    
    request.on('error', (error) => {
      reject(error);
    });
    
    request.setTimeout(10000, () => {
      request.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

// Test functions
async function testRecipesWithVideosEndpoint() {
  console.log('🧪 Testing recipes with videos endpoint...');
  try {
    const response = await makeRequest(`${apiUrl}/api/recipes/with-videos?limit=5`);
    
    if (response.status === 200) {
      console.log('✅ Recipes with videos endpoint is working');
      
      if (Array.isArray(response.data) && response.data.length > 0) {
        console.log(`✅ Found ${response.data.length} recipes`);
        
        // Check if recipes have Spanish content
        const spanishRecipes = response.data.filter(recipe => 
          recipe.title && (
            recipe.title.includes('Pollo') ||
            recipe.title.includes('Pasta') ||
            recipe.title.includes('Pan') ||
            recipe.cuisine_type === 'Italiana' ||
            recipe.cuisine_type === 'Española' ||
            recipe.difficulty === 'Fácil'
          )
        );
        
        if (spanishRecipes.length > 0) {
          console.log(`✅ Found ${spanishRecipes.length} recipes with Spanish content`);
          console.log('   Sample Spanish recipe:', spanishRecipes[0].title);
        } else {
          console.log('⚠️  No recipes with obvious Spanish content found');
        }
        
        // Check if recipes have video information
        const recipesWithThumbnails = response.data.filter(recipe => recipe.video_thumbnail_path);
        console.log(`✅ ${recipesWithThumbnails.length} recipes have video thumbnails`);
        
        // Check if recipes have video titles
        const recipesWithVideoTitles = response.data.filter(recipe => recipe.video_title);
        console.log(`✅ ${recipesWithVideoTitles.length} recipes have video titles`);
        
      } else {
        console.log('⚠️  No recipes found in the response');
      }
    } else {
      console.log(`❌ Recipes endpoint returned status ${response.status}`);
    }
  } catch (error) {
    console.log('❌ Error testing recipes endpoint:', error.message);
  }
}

async function testRecipeExtraction() {
  console.log('\n🧪 Testing recipe extraction with Spanish output...');
  try {
    // Try to extract a recipe from video 1 (if it doesn't already have one)
    const response = await makeRequest(`${apiUrl}/api/recipes/video/1/extract`);
    
    if (response.status === 200) {
      console.log('✅ Recipe extraction endpoint is working');
      if (response.data.recipe) {
        console.log('✅ Recipe extracted successfully');
        
        // Check if the extracted recipe is in Spanish
        const recipe = response.data.recipe;
        const hasSpanishContent = 
          (recipe.title && /[ñáéíóúü]/i.test(recipe.title)) ||
          (recipe.description && /[ñáéíóúü]/i.test(recipe.description)) ||
          (recipe.difficulty && ['Fácil', 'Medio', 'Difícil'].includes(recipe.difficulty)) ||
          (recipe.ingredients && recipe.ingredients.some(ing => /[ñáéíóúü]/i.test(ing.name)));
        
        if (hasSpanishContent) {
          console.log('✅ Extracted recipe contains Spanish content');
        } else {
          console.log('⚠️  Extracted recipe may not be in Spanish');
        }
      }
    } else if (response.status === 400) {
      console.log('ℹ️  Recipe extraction skipped (video may already have a recipe or no transcript)');
    } else {
      console.log(`❌ Recipe extraction returned status ${response.status}`);
    }
  } catch (error) {
    console.log('❌ Error testing recipe extraction:', error.message);
  }
}

async function testFrontendAccess() {
  console.log('\n🧪 Testing frontend access...');
  try {
    const response = await makeRequest(`${baseUrl}/recetas`);
    
    if (response.status === 200) {
      console.log('✅ Frontend recipes page is accessible');
      
      // Check if the response contains Spanish content
      const htmlContent = response.data;
      if (typeof htmlContent === 'string') {
        if (htmlContent.includes('Recetas') || htmlContent.includes('recetas')) {
          console.log('✅ Frontend contains Spanish recipe content');
        } else {
          console.log('⚠️  Frontend may not contain Spanish recipe content');
        }
      }
    } else {
      console.log(`❌ Frontend recipes page returned status ${response.status}`);
    }
  } catch (error) {
    console.log('❌ Error testing frontend access:', error.message);
  }
}

async function testRecipeCuisinesEndpoint() {
  console.log('\n🧪 Testing recipe cuisines endpoint...');
  try {
    const response = await makeRequest(`${apiUrl}/api/recipes/cuisines/list`);
    
    if (response.status === 200) {
      console.log('✅ Recipe cuisines endpoint is working');
      
      if (Array.isArray(response.data) && response.data.length > 0) {
        console.log(`✅ Found ${response.data.length} cuisine types`);
        
        // Check for Spanish cuisine types
        const spanishCuisines = response.data.filter(cuisine => 
          cuisine.includes('Española') || 
          cuisine.includes('Italiana') || 
          cuisine.includes('Latina') ||
          /[ñáéíóúü]/i.test(cuisine)
        );
        
        if (spanishCuisines.length > 0) {
          console.log('✅ Found Spanish cuisine types:', spanishCuisines.join(', '));
        } else {
          console.log('⚠️  No obvious Spanish cuisine types found');
        }
      } else {
        console.log('⚠️  No cuisine types found');
      }
    } else {
      console.log(`❌ Recipe cuisines endpoint returned status ${response.status}`);
    }
  } catch (error) {
    console.log('❌ Error testing recipe cuisines endpoint:', error.message);
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting Spanish Recipe Features API Tests\n');
  
  await testRecipesWithVideosEndpoint();
  await testRecipeExtraction();
  await testFrontendAccess();
  await testRecipeCuisinesEndpoint();
  
  console.log('\n✨ Tests completed!');
  console.log('\n📋 Summary:');
  console.log('- Recipe extraction has been updated to force Spanish output');
  console.log('- Navigation has been updated to show "Recetas" instead of "Recipes"');
  console.log('- Recipes page has been translated to Spanish');
  console.log('- New API endpoint provides recipes with video thumbnails');
  console.log('- Recipe cards now display video thumbnails when available');
  console.log('\n🎯 Next steps:');
  console.log('- Run full Playwright tests when browser dependencies are available');
  console.log('- Test recipe extraction on more cooking videos');
  console.log('- Verify all Spanish translations are complete');
}

// Run the tests
runTests().catch(console.error);
