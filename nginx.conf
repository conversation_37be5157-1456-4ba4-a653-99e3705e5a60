events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:8000;
    }

    upstream frontend {
        server frontend:3000;
    }

    server {
        listen 80;
        server_name localhost;

        # Frontend routes
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket support for hot reload
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # API routes - handle base router paths (e.g., /api/videos)
        location ~ ^/api/(videos|tags|analytics|export)$ {
            # Add trailing slash for FastAPI router base paths and preserve query parameters
            proxy_pass http://backend/$1/?$args;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Handle large file uploads
            client_max_body_size 1G;
            proxy_read_timeout 600s;
            proxy_connect_timeout 300s;
            proxy_send_timeout 600s;
        }

        # API routes - handle sub-paths (e.g., /api/videos/123, /api/tags/cloud/data)
        location ~ ^/api/(videos|tags|analytics|export)/(.*)$ {
            # Pass sub-paths directly and preserve query parameters
            proxy_pass http://backend/$1/$2?$args;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Handle large file uploads
            client_max_body_size 1G;
            proxy_read_timeout 600s;
            proxy_connect_timeout 300s;
            proxy_send_timeout 600s;
        }

        # API routes - general handling for other endpoints (like /health)
        location /api/ {
            # Strip /api prefix and pass to backend
            rewrite ^/api/(.*) /$1 break;
            proxy_pass http://backend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Handle large file uploads
            client_max_body_size 1G;
            proxy_read_timeout 600s;
            proxy_connect_timeout 300s;
            proxy_send_timeout 600s;
        }

        # Serve video files directly
        location /static/videos/ {
            alias /usr/share/nginx/html/videos/;
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, OPTIONS";
            add_header Access-Control-Allow-Headers "Range";

            # Enable range requests for video streaming
            add_header Accept-Ranges bytes;
        }
    }
}
