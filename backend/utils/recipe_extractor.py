import json
import re
import requests
from typing import Dict, List, Optional, Tuple
from models.schemas import RecipeCreate, IngredientBase, InstructionStep


class RecipeExtractor:
    """Extract recipe information from video transcripts using AI"""
    
    def __init__(self):
        self.ollama_endpoints = [
            'http://ollama:11434/api/generate',  # Docker service
            'http://localhost:11434/api/generate'  # Local fallback
        ]
    
    async def extract_recipe(self, transcript: str, video_title: str = "", video_id: int = None) -> Optional[RecipeCreate]:
        """Extract recipe from transcript if it's a cooking video"""
        try:
            # First, check if this is actually a recipe/cooking video
            if not self._is_cooking_video(transcript, video_title):
                return None
            
            # Extract recipe using Ollama
            recipe_data = await self._extract_recipe_with_ollama(transcript, video_title)
            
            if recipe_data and video_id:
                # Convert to RecipeCreate schema
                return self._convert_to_recipe_create(recipe_data, video_id)
            
            return None
            
        except Exception as e:
            print(f"Recipe extraction failed: {e}")
            return None
    
    def _is_cooking_video(self, transcript: str, title: str = "") -> bool:
        """Determine if this is a cooking/recipe video"""
        text = f"{title} {transcript}".lower()
        
        # Cooking keywords in multiple languages
        cooking_keywords = [
            # English
            'recipe', 'cook', 'bake', 'fry', 'boil', 'ingredients', 'kitchen', 'oven',
            'stir', 'mix', 'chop', 'slice', 'dice', 'season', 'taste', 'serve',
            'tablespoon', 'teaspoon', 'cup', 'pound', 'ounce', 'gram', 'liter',
            'minutes', 'degrees', 'heat', 'oil', 'salt', 'pepper', 'garlic',
            
            # Spanish
            'receta', 'cocinar', 'hornear', 'freír', 'hervir', 'ingredientes', 'cocina', 'horno',
            'mezclar', 'revolver', 'picar', 'cortar', 'sazonar', 'probar', 'servir',
            'cucharada', 'cucharadita', 'taza', 'gramo', 'litro', 'minutos', 'grados',
            'aceite', 'sal', 'pimienta', 'ajo', 'cebolla', 'tomate', 'pollo', 'carne',
            'pasta', 'arroz', 'verduras', 'queso', 'huevo', 'leche', 'azúcar', 'harina'
        ]
        
        # Count cooking-related words
        cooking_word_count = sum(1 for keyword in cooking_keywords if keyword in text)
        
        # Consider it a cooking video if it has enough cooking keywords
        return cooking_word_count >= 3
    
    async def _extract_recipe_with_ollama(self, transcript: str, title: str = "") -> Optional[Dict]:
        """Extract recipe using Ollama LLM"""
        try:
            prompt = f'''You are an expert chef and recipe analyzer. Analyze this cooking video transcript and extract a complete recipe with ingredients and step-by-step instructions.

VIDEO TITLE: "{title}"
TRANSCRIPT: "{transcript}"

TASK: Extract the recipe information and return it as a JSON object with this exact structure:

{{
  "title": "Recipe name (if mentioned, otherwise create descriptive name)",
  "description": "Brief description of the dish",
  "ingredients": [
    {{
      "name": "ingredient name",
      "amount": "quantity with unit (e.g., '2 cups', '1 tbsp', '500g')",
      "unit": "unit only (e.g., 'cups', 'tbsp', 'g')",
      "notes": "preparation notes (e.g., 'finely chopped', 'room temperature')"
    }}
  ],
  "instructions": [
    {{
      "step_number": 1,
      "instruction": "Detailed step instruction",
      "time": "cooking time if mentioned (e.g., '5 minutes', 'until golden')",
      "temperature": "temperature if mentioned (e.g., '350°F', 'medium heat')"
    }}
  ],
  "prep_time": "preparation time (e.g., '15 minutes')",
  "cook_time": "cooking time (e.g., '30 minutes')",
  "total_time": "total time (e.g., '45 minutes')",
  "servings": "number of servings (e.g., '4 servings', '6 portions')",
  "difficulty": "Easy, Medium, or Hard",
  "cuisine_type": "cuisine type (e.g., 'Italian', 'Mexican', 'Asian')",
  "confidence": 0.85
}}

IMPORTANT GUIDELINES:
1. Extract ALL ingredients mentioned with their quantities
2. Break down instructions into clear, numbered steps
3. Preserve the original language (Spanish ingredients for Spanish videos)
4. Include cooking times and temperatures when mentioned
5. If quantities are unclear, use "to taste" or "as needed"
6. Set confidence between 0.0-1.0 based on how clear the recipe is
7. If no clear recipe is found, return {{"confidence": 0.0}}

JSON Response:'''

            print(f"Extracting recipe from transcript: {transcript[:100]}...")
            
            # Try Ollama endpoints
            for endpoint in self.ollama_endpoints:
                try:
                    print(f"Trying Ollama endpoint: {endpoint}")
                    response = requests.post(
                        endpoint,
                        json={
                            'model': 'llama3.2:3b',
                            'prompt': prompt,
                            'stream': False,
                            'options': {
                                'temperature': 0.3,  # Low temperature for consistent extraction
                                'top_p': 0.9,
                                'num_predict': 1000,  # Allow longer responses for recipes
                                'stop': ['\n\nHuman:', '\n\nAssistant:']
                            }
                        },
                        timeout=120  # Longer timeout for recipe extraction
                    )
                    
                    if response.status_code == 200:
                        print(f"Successfully connected to Ollama at {endpoint}")
                        result = response.json()
                        generated_text = result.get('response', '').strip()
                        print(f"Ollama recipe response: {generated_text[:200]}...")
                        
                        # Parse the JSON response
                        recipe_data = self._parse_recipe_response(generated_text)
                        
                        if recipe_data and recipe_data.get('confidence', 0) > 0.5:
                            print(f"Recipe extracted with confidence: {recipe_data.get('confidence')}")
                            return recipe_data
                        else:
                            print("Low confidence recipe extraction, skipping")
                            return None
                            
                except requests.exceptions.RequestException as e:
                    print(f"Failed to connect to {endpoint}: {e}")
                    continue
            
            print("All Ollama endpoints failed")
            return None
            
        except Exception as e:
            print(f"Recipe extraction with Ollama failed: {e}")
            return None
    
    def _parse_recipe_response(self, response_text: str) -> Optional[Dict]:
        """Parse Ollama response and extract recipe JSON"""
        try:
            # Try to find JSON in the response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                recipe_data = json.loads(json_str)
                return recipe_data
            else:
                print("No JSON found in recipe response")
                return None
                
        except json.JSONDecodeError as e:
            print(f"Failed to parse recipe JSON: {e}")
            return None
    
    def _convert_to_recipe_create(self, recipe_data: Dict, video_id: int) -> RecipeCreate:
        """Convert extracted recipe data to RecipeCreate schema"""
        try:
            # Convert ingredients
            ingredients = []
            for ing_data in recipe_data.get('ingredients', []):
                ingredient = IngredientBase(
                    name=ing_data.get('name', ''),
                    amount=ing_data.get('amount'),
                    unit=ing_data.get('unit'),
                    notes=ing_data.get('notes')
                )
                ingredients.append(ingredient)
            
            # Convert instructions
            instructions = []
            for inst_data in recipe_data.get('instructions', []):
                instruction = InstructionStep(
                    step_number=inst_data.get('step_number', len(instructions) + 1),
                    instruction=inst_data.get('instruction', ''),
                    time=inst_data.get('time'),
                    temperature=inst_data.get('temperature')
                )
                instructions.append(instruction)
            
            # Create RecipeCreate object
            recipe_create = RecipeCreate(
                video_id=video_id,
                title=recipe_data.get('title'),
                description=recipe_data.get('description'),
                ingredients=ingredients,
                instructions=instructions,
                prep_time=recipe_data.get('prep_time'),
                cook_time=recipe_data.get('cook_time'),
                total_time=recipe_data.get('total_time'),
                servings=recipe_data.get('servings'),
                difficulty=recipe_data.get('difficulty'),
                cuisine_type=recipe_data.get('cuisine_type'),
                extraction_confidence=recipe_data.get('confidence', 0.0)
            )
            
            return recipe_create
            
        except Exception as e:
            print(f"Failed to convert recipe data: {e}")
            return None
