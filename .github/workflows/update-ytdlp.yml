name: Update yt-dlp

on:
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * 1'
  workflow_dispatch: # Allow manual trigger

permissions:
  contents: write
  pull-requests: write

jobs:
  check-ytdlp-update:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install requests

    - name: Check for yt-dlp updates
      id: check-update
      run: |
        # Get current version from requirements.txt
        CURRENT_VERSION=$(grep "yt-dlp>=" backend/requirements.txt | sed 's/yt-dlp>=//')
        echo "Current version: $CURRENT_VERSION"
        echo "current_version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
        
        # Get latest version from PyPI
        LATEST_VERSION=$(python -c "
        import requests
        response = requests.get('https://pypi.org/pypi/yt-dlp/json')
        data = response.json()
        print(data['info']['version'])
        ")
        echo "Latest version: $LATEST_VERSION"
        
        # Compare versions
        if [ "$CURRENT_VERSION" != "$LATEST_VERSION" ]; then
          echo "update_available=true" >> $GITHUB_OUTPUT
          echo "new_version=$LATEST_VERSION" >> $GITHUB_OUTPUT
          echo "Update available: $CURRENT_VERSION -> $LATEST_VERSION"
        else
          echo "update_available=false" >> $GITHUB_OUTPUT
          echo "No update available"
        fi

    - name: Update requirements.txt
      if: steps.check-update.outputs.update_available == 'true'
      run: |
        # Update yt-dlp version in requirements.txt
        sed -i "s/yt-dlp>=.*/yt-dlp>=${{ steps.check-update.outputs.new_version }}/" backend/requirements.txt

    - name: Create branch and push changes
      if: steps.check-update.outputs.update_available == 'true'
      run: |
        # Generate unique branch name with timestamp
        TIMESTAMP=$(date +%Y%m%d-%H%M%S)
        BRANCH_NAME="auto-update-ytdlp-${{ steps.check-update.outputs.new_version }}-${TIMESTAMP}"
        echo "branch_name=${BRANCH_NAME}" >> $GITHUB_OUTPUT

        # Create and switch to new branch
        git checkout -b ${BRANCH_NAME}

        # Configure git
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"

        # Add and commit changes
        git add backend/requirements.txt
        git commit -m "chore: update yt-dlp to ${{ steps.check-update.outputs.new_version }}"

        # Push the branch
        git push origin ${BRANCH_NAME}

        # Output information for manual PR creation
        echo "::notice title=Update Available::yt-dlp update available from ${{ steps.check-update.outputs.current_version }} to ${{ steps.check-update.outputs.new_version }}"
        echo "::notice title=Branch Created::Branch '${BRANCH_NAME}' has been created and pushed"
        echo "::notice title=Manual Action Required::Please create a pull request manually from the branch '${BRANCH_NAME}' to 'dev'"
      id: create-branch
    - name: Create Pull Request URL
      if: steps.check-update.outputs.update_available == 'true'
      run: |
        BRANCH_NAME="${{ steps.create-branch.outputs.branch_name }}"
        PR_URL="https://github.com/${{ github.repository }}/compare/dev...${BRANCH_NAME}?quick_pull=1&title=%F0%9F%A4%96%20Auto-update%20yt-dlp%20to%20${{ steps.check-update.outputs.new_version }}&body=%23%23%20%F0%9F%A4%96%20Automated%20yt-dlp%20Update%20%28Testing%20in%20dev%29%0A%0AThis%20PR%20updates%20yt-dlp%20from%20${{ steps.check-update.outputs.current_version }}%20to%20${{ steps.check-update.outputs.new_version }}.%0A%0A%23%23%23%20Changes%3A%0A-%20Updated%20yt-dlp%20version%20in%20%60backend%2Frequirements.txt%60%0A%0A%23%23%23%20Testing%20Workflow%3A%0A1.%20%E2%9C%85%20Merge%20this%20PR%20to%20%60dev%60%20branch%0A2.%20%F0%9F%A7%AA%20Test%20the%20updated%20version%20in%20dev%20environment%0A3.%20%F0%9F%93%9D%20Create%20PR%20from%20%60dev%60%20to%20%60main%60%20when%20testing%20is%20complete%0A%0A%23%23%23%20Testing%20Checklist%3A%0A-%20%5B%20%5D%20Verify%20downloads%20still%20work%20correctly%0A-%20%5B%20%5D%20Test%20with%20various%20video%20platforms%20%28YouTube%2C%20etc.%29%0A-%20%5B%20%5D%20Check%20for%20any%20breaking%20changes%0A-%20%5B%20%5D%20Verify%20backend%20functionality%0A%0A---%0A*This%20PR%20targets%20the%20%60dev%60%20branch%20for%20testing%20before%20merging%20to%20%60main%60.*"

        echo "::notice title=🤖 yt-dlp Update Available::Update from ${{ steps.check-update.outputs.current_version }} to ${{ steps.check-update.outputs.new_version }}"
        echo "::notice title=📝 Create PR::Click here to create PR: ${PR_URL}"
        echo "::notice title=🔗 Quick Link::Branch '${BRANCH_NAME}' is ready for PR creation"

        # Also output the URL for easy access
        echo "PR_URL=${PR_URL}" >> $GITHUB_OUTPUT
      id: create-pr-url