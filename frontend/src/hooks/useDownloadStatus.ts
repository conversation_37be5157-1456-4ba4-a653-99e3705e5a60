import { useQuery } from '@tanstack/react-query';
import { videoApi } from '../utils/api';

interface DownloadStatus {
  video_id: number;
  download_status: string;
  download_progress: number;
  download_error?: string;
  processing_status: string;
  processing_progress: number;
}

export const useDownloadStatus = (videoId: number, enabled: boolean = true) => {
  return useQuery<DownloadStatus>({
    queryKey: ['downloadStatus', videoId],
    queryFn: () => videoApi.getDownloadStatus(videoId),
    enabled: enabled && videoId > 0,
    refetchInterval: (query) => {
      // Stop polling if download is completed or failed
      const data = query.state.data;
      if (data?.download_status === 'completed' || data?.download_status === 'failed') {
        return false;
      }
      // Poll every 2 seconds while downloading
      return 2000;
    },
    retry: 3,
    retryDelay: 1000,
  });
};

export default useDownloadStatus;
