const { test, expect } = require('@playwright/test');

test.describe('Spanish Recipe Features', () => {
  const baseUrl = 'http://**************:3000';

  test.beforeEach(async ({ page }) => {
    // Navigate to the home page
    await page.goto(baseUrl);
    await page.waitForLoadState('networkidle');
  });

  test('should have Recetas navigation link', async ({ page }) => {
    // Check that the "Recetas" navigation link exists
    const recetasLink = page.locator('nav a[href="/recetas"]');
    await expect(recetasLink).toBeVisible();
    await expect(recetasLink).toHaveText('Recetas');
  });

  test('should navigate to recipes page when clicking Recetas', async ({ page }) => {
    // Click the Recetas navigation link
    await page.click('nav a[href="/recetas"]');
    
    // Wait for navigation and page load
    await page.waitForURL('**/recetas');
    await page.waitForLoadState('networkidle');
    
    // Verify we're on the recipes page
    expect(page.url()).toContain('/recetas');
    
    // Check for Spanish page title
    const pageTitle = page.locator('h1');
    await expect(pageTitle).toContainText('Recetas');
  });

  test('should display recipes page with Spanish interface', async ({ page }) => {
    // Navigate to recipes page
    await page.goto(`${baseUrl}/recetas`);
    await page.waitForLoadState('networkidle');
    
    // Check Spanish interface elements
    await expect(page.locator('h1')).toContainText('🍳 Recetas');
    await expect(page.locator('text=Descubre deliciosas recetas')).toBeVisible();
    
    // Check search placeholder is in Spanish
    const searchInput = page.locator('input[placeholder*="Buscar"]');
    await expect(searchInput).toBeVisible();
    
    // Check filter labels are in Spanish
    await expect(page.locator('text=Filtros')).toBeVisible();
  });

  test('should display recipe cards with video thumbnails', async ({ page }) => {
    // Navigate to recipes page
    await page.goto(`${baseUrl}/recetas`);
    await page.waitForLoadState('networkidle');
    
    // Wait for recipes to load
    await page.waitForSelector('[data-testid="recipe-card"], .bg-white.dark\\:bg-gray-800', { timeout: 10000 });
    
    // Check if recipe cards are displayed
    const recipeCards = page.locator('.bg-white.dark\\:bg-gray-800.rounded-lg');
    const cardCount = await recipeCards.count();
    
    if (cardCount > 0) {
      // Check first recipe card
      const firstCard = recipeCards.first();
      await expect(firstCard).toBeVisible();
      
      // Check for recipe title
      const recipeTitle = firstCard.locator('h3');
      await expect(recipeTitle).toBeVisible();
      
      // Check for recipe stats in Spanish
      await expect(firstCard.locator('text=ingredientes')).toBeVisible();
      await expect(firstCard.locator('text=pasos')).toBeVisible();
      
      // Check for thumbnail or placeholder
      const thumbnail = firstCard.locator('img, .aspect-video');
      if (await thumbnail.count() > 0) {
        await expect(thumbnail.first()).toBeVisible();
      }
    }
  });

  test('should navigate to video page when clicking recipe card', async ({ page }) => {
    // Navigate to recipes page
    await page.goto(`${baseUrl}/recetas`);
    await page.waitForLoadState('networkidle');
    
    // Wait for recipes to load
    await page.waitForSelector('.bg-white.dark\\:bg-gray-800.rounded-lg', { timeout: 10000 });
    
    const recipeCards = page.locator('.bg-white.dark\\:bg-gray-800.rounded-lg');
    const cardCount = await recipeCards.count();
    
    if (cardCount > 0) {
      // Click the first recipe card
      await recipeCards.first().click();
      
      // Wait for navigation to video page
      await page.waitForURL('**/video/**');
      
      // Verify we're on a video page
      expect(page.url()).toMatch(/\/video\/\d+/);
      
      // Check that video page loaded
      await page.waitForLoadState('networkidle');
    }
  });

  test('should display recipe information on video page', async ({ page }) => {
    // Navigate directly to a video with a recipe (video 5 has a recipe based on our API test)
    await page.goto(`${baseUrl}/video/5`);
    await page.waitForLoadState('networkidle');
    
    // Check if recipe information is displayed
    const recipeSection = page.locator('[data-testid="recipe-section"], .recipe-card, text=Receta');
    
    // If recipe exists, verify it's displayed
    if (await recipeSection.count() > 0) {
      await expect(recipeSection.first()).toBeVisible();
    }
  });

  test('should search recipes in Spanish', async ({ page }) => {
    // Navigate to recipes page
    await page.goto(`${baseUrl}/recetas`);
    await page.waitForLoadState('networkidle');
    
    // Search for Spanish terms
    const searchInput = page.locator('input[placeholder*="Buscar"]');
    await searchInput.fill('pollo');
    await searchInput.press('Enter');
    
    // Wait for search results
    await page.waitForLoadState('networkidle');
    
    // Check that search was performed (results count should be visible)
    const resultsText = page.locator('text=receta');
    await expect(resultsText).toBeVisible();
  });

  test('should filter recipes by cuisine type in Spanish', async ({ page }) => {
    // Navigate to recipes page
    await page.goto(`${baseUrl}/recetas`);
    await page.waitForLoadState('networkidle');
    
    // Open filters
    const filtersButton = page.locator('text=Filtros');
    await filtersButton.click();
    
    // Check that filter labels are in Spanish
    await expect(page.locator('text=Tipo de Cocina')).toBeVisible();
    await expect(page.locator('text=Dificultad')).toBeVisible();
    
    // Check filter options are in Spanish
    const cuisineSelect = page.locator('select').first();
    await expect(page.locator('option[value=""]')).toContainText('Todas las Cocinas');
  });

  test('should display Spanish recipe content', async ({ page }) => {
    // Navigate to recipes page
    await page.goto(`${baseUrl}/recetas`);
    await page.waitForLoadState('networkidle');
    
    // Wait for recipes to load
    await page.waitForSelector('.bg-white.dark\\:bg-gray-800.rounded-lg', { timeout: 10000 });
    
    const recipeCards = page.locator('.bg-white.dark\\:bg-gray-800.rounded-lg');
    const cardCount = await recipeCards.count();
    
    if (cardCount > 0) {
      // Look for Spanish recipe content
      const spanishRecipes = page.locator('text=/Pollo|Pasta|Pan|Receta|minutos|ingredientes|pasos/i');
      
      if (await spanishRecipes.count() > 0) {
        await expect(spanishRecipes.first()).toBeVisible();
      }
    }
  });

  test('should handle recipe extraction for cooking videos', async ({ page }) => {
    // This test checks if recipe extraction can be triggered
    // Navigate to a video page (using video 1 which doesn't have a recipe yet)
    await page.goto(`${baseUrl}/video/1`);
    await page.waitForLoadState('networkidle');
    
    // Check if there's a way to extract recipes (this might be an admin feature)
    // For now, just verify the page loads correctly
    await expect(page.locator('h1')).toBeVisible();
  });

  test('should export recipe in Spanish format', async ({ page }) => {
    // Navigate to recipes page
    await page.goto(`${baseUrl}/recetas`);
    await page.waitForLoadState('networkidle');
    
    // Wait for recipes to load
    await page.waitForSelector('.bg-white.dark\\:bg-gray-800.rounded-lg', { timeout: 10000 });
    
    const recipeCards = page.locator('.bg-white.dark\\:bg-gray-800.rounded-lg');
    const cardCount = await recipeCards.count();
    
    if (cardCount > 0) {
      // Look for export button (download icon)
      const exportButton = recipeCards.first().locator('button[title*="Exportar"], svg');
      
      if (await exportButton.count() > 0) {
        // Set up download handler
        const downloadPromise = page.waitForEvent('download');
        
        // Click export button
        await exportButton.first().click();
        
        // Wait for download (with timeout)
        try {
          const download = await downloadPromise;
          expect(download.suggestedFilename()).toMatch(/\.txt$/);
        } catch (error) {
          // Download might not be available in test environment
          console.log('Download test skipped - not available in test environment');
        }
      }
    }
  });
});
