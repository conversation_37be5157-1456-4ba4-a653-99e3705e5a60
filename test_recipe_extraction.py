#!/usr/bin/env python3
"""
Test script to demonstrate recipe extraction functionality
"""

import requests
import json

BASE_URL = "http://**************:8090"

def test_recipe_extraction():
    """Test recipe extraction functionality"""
    print("🍳 Testing tagTok Recipe Extraction System")
    print("=" * 50)
    
    # Test 1: Get all recipes
    print("\n📋 Test 1: Getting all recipes...")
    try:
        response = requests.get(f"{BASE_URL}/api/recipes/")
        if response.status_code == 200:
            recipes = response.json()
            print(f"✅ Found {len(recipes)} recipes")
            
            for recipe in recipes:
                print(f"   🥘 {recipe['title']} (Video ID: {recipe['video_id']})")
                print(f"      📝 {recipe['description']}")
                print(f"      ⏱️  {recipe['total_time']} | 🍽️  {recipe['servings']} | 📊 {recipe['difficulty']}")
                print(f"      🥘 {len(recipe['ingredients'])} ingredients | 📋 {len(recipe['instructions'])} steps")
                print(f"      🎯 Confidence: {recipe['extraction_confidence']}")
                print()
        else:
            print(f"❌ Failed to get recipes: {response.status_code}")
    except Exception as e:
        print(f"❌ Error getting recipes: {e}")
    
    # Test 2: Get recipe for specific video
    print("\n📋 Test 2: Getting recipe for video 42...")
    try:
        response = requests.get(f"{BASE_URL}/api/recipes/video/42")
        if response.status_code == 200:
            recipe = response.json()
            print(f"✅ Recipe found: {recipe['title']}")
            
            print(f"\n📝 Description: {recipe['description']}")
            print(f"⏱️  Prep: {recipe['prep_time']} | Cook: {recipe['cook_time']} | Total: {recipe['total_time']}")
            print(f"🍽️  Servings: {recipe['servings']} | 📊 Difficulty: {recipe['difficulty']}")
            print(f"🌍 Cuisine: {recipe['cuisine_type']}")
            
            print(f"\n🥘 Ingredients ({len(recipe['ingredients'])}):")
            for ing in recipe['ingredients']:
                amount = f"{ing['amount']} {ing['unit']}".strip()
                notes = f" ({ing['notes']})" if ing['notes'] else ""
                print(f"   • {amount} {ing['name']}{notes}")
            
            print(f"\n📋 Instructions ({len(recipe['instructions'])}):")
            for step in recipe['instructions']:
                time_temp = []
                if step['time']:
                    time_temp.append(f"⏱️ {step['time']}")
                if step['temperature']:
                    time_temp.append(f"🌡️ {step['temperature']}")
                extra = f" ({', '.join(time_temp)})" if time_temp else ""
                print(f"   {step['step_number']}. {step['instruction']}{extra}")
                
        else:
            print(f"❌ Failed to get recipe: {response.status_code}")
    except Exception as e:
        print(f"❌ Error getting recipe: {e}")
    
    # Test 3: Extract recipe from another cooking video
    print("\n📋 Test 3: Testing recipe extraction on video 68 (Spanish cooking video)...")
    try:
        response = requests.post(f"{BASE_URL}/api/recipes/video/68/extract")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ {result['message']}")
            if result.get('recipe'):
                recipe = result['recipe']
                print(f"   🥘 Recipe: {recipe['title']}")
                print(f"   🎯 Confidence: {recipe['extraction_confidence']}")
                print(f"   🥘 Ingredients: {len(recipe['ingredients'])}")
                print(f"   📋 Steps: {len(recipe['instructions'])}")
        else:
            print(f"❌ Failed to extract recipe: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Error extracting recipe: {e}")
    
    print("\n🎉 Recipe extraction testing completed!")
    print("\n📋 Available API endpoints:")
    print("   • GET /api/recipes/ - List all recipes")
    print("   • GET /api/recipes/video/{video_id} - Get recipe for specific video")
    print("   • POST /api/recipes/video/{video_id}/extract - Extract recipe from video")
    print("   • GET /api/recipes/{recipe_id} - Get specific recipe")
    print("   • PUT /api/recipes/{recipe_id} - Update recipe")
    print("   • DELETE /api/recipes/{recipe_id} - Delete recipe")

if __name__ == "__main__":
    test_recipe_extraction()
