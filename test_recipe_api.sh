#!/bin/bash

# Recipe Feature Testing Script
# Tests all the new recipe functionality in tagTok

BASE_URL="http://**************:8090"
echo "🧪 Testing Recipe Features"
echo "=========================="

# Test 1: Get all recipes
echo ""
echo "📋 Test 1: Getting all recipes..."
RECIPES=$(curl -s "$BASE_URL/api/recipes/" | jq '.')
RECIPE_COUNT=$(echo "$RECIPES" | jq 'length')
echo "✅ Found $RECIPE_COUNT recipes"

# Show first few recipes
echo "$RECIPES" | jq -r '.[:3][] | "   🥘 \(.title // "Untitled") - \(.cuisine_type // "Unknown cuisine") - \(.difficulty // "Unknown difficulty")"'

# Test 2: Test video with recipe
echo ""
echo "📋 Test 2: Testing video with recipe..."
VIDEO_ID=$(echo "$RECIPES" | jq -r '.[0].video_id')
if [ "$VIDEO_ID" != "null" ]; then
    echo "✅ Testing video ID: $VIDEO_ID"
    VIDEO_DATA=$(curl -s "$BASE_URL/videos/$VIDEO_ID" | jq '.')
    HAS_RECIPE=$(echo "$VIDEO_DATA" | jq '.recipe != null')
    if [ "$HAS_RECIPE" = "true" ]; then
        echo "✅ Video includes recipe data"
        echo "$VIDEO_DATA" | jq -r '.recipe | "   📝 Recipe: \(.title // "Untitled")"'
        echo "$VIDEO_DATA" | jq -r '.recipe | "   🍽️  Servings: \(.servings // "Unknown")"'
        echo "$VIDEO_DATA" | jq -r '.recipe | "   ⏱️  Total time: \(.total_time // "Unknown")"'
    else
        echo "❌ Video does not include recipe data"
    fi
else
    echo "⚠️  No video ID found in recipes"
fi

# Test 3: Test recipe search
echo ""
echo "📋 Test 3: Testing recipe search..."
SEARCH_RESULTS=$(curl -s "$BASE_URL/api/recipes/?search=chicken" | jq '.')
SEARCH_COUNT=$(echo "$SEARCH_RESULTS" | jq 'length')
echo "✅ Found $SEARCH_COUNT recipes containing 'chicken'"
echo "$SEARCH_RESULTS" | jq -r '.[:2][] | "   🐔 \(.title // "Untitled")"'

# Test 4: Test cuisine filtering
echo ""
echo "📋 Test 4: Testing cuisine filtering..."
ITALIAN_RECIPES=$(curl -s "$BASE_URL/api/recipes/?cuisine=Italian" | jq '.')
ITALIAN_COUNT=$(echo "$ITALIAN_RECIPES" | jq 'length')
echo "✅ Found $ITALIAN_COUNT Italian recipes"

# Test 5: Test difficulty filtering
echo ""
echo "📋 Test 5: Testing difficulty filtering..."
EASY_RECIPES=$(curl -s "$BASE_URL/api/recipes/?difficulty=Easy" | jq '.')
EASY_COUNT=$(echo "$EASY_RECIPES" | jq 'length')
echo "✅ Found $EASY_COUNT easy recipes"

# Test 6: Test ingredient search
echo ""
echo "📋 Test 6: Testing ingredient search..."
INGREDIENT_SEARCH=$(curl -s "$BASE_URL/api/recipes/search/ingredients?ingredients=onion,garlic" | jq '.')
INGREDIENT_COUNT=$(echo "$INGREDIENT_SEARCH" | jq 'length')
echo "✅ Found $INGREDIENT_COUNT recipes with onion and garlic"

# Test 7: Test recipe metadata endpoints
echo ""
echo "📋 Test 7: Testing recipe metadata..."

# Test cuisines list
CUISINES=$(curl -s "$BASE_URL/api/recipes/cuisines/list" | jq '.')
CUISINE_COUNT=$(echo "$CUISINES" | jq 'length')
echo "✅ Found $CUISINE_COUNT cuisine types:"
echo "$CUISINES" | jq -r '.[:5][] | "   🌍 \(.)"'

# Test difficulties list
DIFFICULTIES=$(curl -s "$BASE_URL/api/recipes/difficulties/list" | jq '.')
echo "✅ Available difficulties:"
echo "$DIFFICULTIES" | jq -r '.[] | "   📊 \(.)"'

# Test 8: Test recipe stats
echo ""
echo "📋 Test 8: Testing recipe statistics..."
STATS=$(curl -s "$BASE_URL/api/recipes/stats/overview" | jq '.')
echo "✅ Recipe Statistics:"
echo "$STATS" | jq -r '"   📊 Total recipes: \(.total_recipes)"'
echo "$STATS" | jq -r '"   🌍 Cuisine types: \(.cuisine_types)"'
echo "$STATS" | jq -r '"   🎯 Average confidence: \(.avg_confidence * 100 | floor)%"'

# Test 9: Show detailed recipe example
echo ""
echo "📋 Test 9: Detailed recipe example..."
FIRST_RECIPE=$(echo "$RECIPES" | jq '.[0]')
echo "✅ Recipe Details:"
echo "$FIRST_RECIPE" | jq -r '"   📝 Title: \(.title // "Untitled")"'
echo "$FIRST_RECIPE" | jq -r '"   📖 Description: \(.description // "No description")"'
echo "$FIRST_RECIPE" | jq -r '"   🥘 Ingredients: \(.ingredients | length)"'
echo "$FIRST_RECIPE" | jq -r '"   📋 Instructions: \(.instructions | length)"'
echo "$FIRST_RECIPE" | jq -r '"   ⏱️  Prep time: \(.prep_time // "Unknown")"'
echo "$FIRST_RECIPE" | jq -r '"   🔥 Cook time: \(.cook_time // "Unknown")"'
echo "$FIRST_RECIPE" | jq -r '"   🍽️  Servings: \(.servings // "Unknown")"'
echo "$FIRST_RECIPE" | jq -r '"   📊 Difficulty: \(.difficulty // "Unknown")"'
echo "$FIRST_RECIPE" | jq -r '"   🌍 Cuisine: \(.cuisine_type // "Unknown")"'
echo "$FIRST_RECIPE" | jq -r '"   🎯 Confidence: \(.extraction_confidence * 100 | floor)%"'

echo ""
echo "   🥘 Ingredients:"
echo "$FIRST_RECIPE" | jq -r '.ingredients[:5][] | "      • \(.amount // "") \(.unit // "") \(.name) \(if .notes then "(\(.notes))" else "" end)"'

echo ""
echo "   📋 Instructions:"
echo "$FIRST_RECIPE" | jq -r '.instructions[:3][] | "      \(.step_number). \(.instruction)"'

echo ""
echo "🎉 Recipe Feature Tests Completed!"
echo ""
echo "📊 Summary:"
echo "   ✅ Recipe API endpoints working"
echo "   ✅ Video-recipe integration working"
echo "   ✅ Recipe search and filtering working"
echo "   ✅ Recipe metadata endpoints working"
echo "   ✅ Recipe statistics working"
echo ""
echo "🔗 Available Recipe Features:"
echo "   • Recipe extraction from cooking videos"
echo "   • Structured ingredient and instruction storage"
echo "   • Recipe search by title, description, ingredients"
echo "   • Recipe filtering by cuisine and difficulty"
echo "   • Recipe export functionality"
echo "   • Multi-language support (Spanish/English)"
echo "   • Automatic recipe detection and extraction"
echo "   • Confidence scoring for extraction quality"
