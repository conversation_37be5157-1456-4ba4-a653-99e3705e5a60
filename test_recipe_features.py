#!/usr/bin/env python3
"""
Playwright test script for recipe features
Tests the new recipe functionality in tagTok
"""

import asyncio
import json
from playwright.async_api import async_playwright

BASE_URL = "http://**************:3000"
API_URL = "http://**************:8090"

async def test_recipe_features():
    """Test recipe functionality using Playwright"""
    async with async_playwright() as p:
        # Launch browser
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()
        
        print("🧪 Starting Recipe Feature Tests")
        print("=" * 50)
        
        try:
            # Test 1: Check if recipes API is working
            print("\n📋 Test 1: Testing Recipe API...")
            
            # Navigate to a page that can make API calls
            await page.goto(f"{BASE_URL}")
            await page.wait_for_load_state('networkidle')
            
            # Test recipe API endpoint
            recipes_response = await page.evaluate(f"""
                fetch('{API_URL}/api/recipes/')
                .then(response => response.json())
                .then(data => data)
                .catch(error => {{ error: error.message }})
            """)
            
            if isinstance(recipes_response, list):
                print(f"✅ Recipe API working - Found {len(recipes_response)} recipes")
                
                # Show first few recipes
                for i, recipe in enumerate(recipes_response[:3]):
                    print(f"   🥘 Recipe {i+1}: {recipe.get('title', 'Untitled')}")
                    print(f"      📊 Confidence: {recipe.get('extraction_confidence', 0):.2f}")
                    print(f"      🥘 Ingredients: {len(recipe.get('ingredients', []))}")
                    print(f"      📋 Steps: {len(recipe.get('instructions', []))}")
            else:
                print(f"❌ Recipe API error: {recipes_response}")
                return
            
            # Test 2: Check video with recipe
            print("\n📋 Test 2: Testing Video with Recipe...")
            
            # Find a video with a recipe
            video_with_recipe = None
            for recipe in recipes_response:
                video_id = recipe.get('video_id')
                if video_id:
                    video_with_recipe = video_id
                    break
            
            if video_with_recipe:
                print(f"✅ Found video with recipe: Video ID {video_with_recipe}")
                
                # Test video API includes recipe
                video_response = await page.evaluate(f"""
                    fetch('{API_URL}/videos/{video_with_recipe}')
                    .then(response => response.json())
                    .then(data => data)
                    .catch(error => {{ error: error.message }})
                """)
                
                if video_response.get('recipe'):
                    print("✅ Video API includes recipe data")
                    recipe = video_response['recipe']
                    print(f"   📝 Recipe: {recipe.get('title', 'Untitled')}")
                    print(f"   🍽️  Servings: {recipe.get('servings', 'Unknown')}")
                    print(f"   ⏱️  Total time: {recipe.get('total_time', 'Unknown')}")
                else:
                    print("❌ Video API does not include recipe data")
            else:
                print("⚠️  No videos with recipes found")
            
            # Test 3: Test recipe search
            print("\n📋 Test 3: Testing Recipe Search...")
            
            search_response = await page.evaluate(f"""
                fetch('{API_URL}/api/recipes/?search=chicken')
                .then(response => response.json())
                .then(data => data)
                .catch(error => {{ error: error.message }})
            """)
            
            if isinstance(search_response, list):
                print(f"✅ Recipe search working - Found {len(search_response)} chicken recipes")
                for recipe in search_response[:2]:
                    print(f"   🐔 {recipe.get('title', 'Untitled')}")
            else:
                print(f"❌ Recipe search error: {search_response}")
            
            # Test 4: Test recipe extraction endpoint
            print("\n📋 Test 4: Testing Recipe Extraction...")
            
            # Find a cooking video without a recipe
            videos_response = await page.evaluate(f"""
                fetch('{API_URL}/videos/')
                .then(response => response.json())
                .then(data => data)
                .catch(error => {{ error: error.message }})
            """)
            
            cooking_video_without_recipe = None
            if isinstance(videos_response, list):
                for video in videos_response:
                    # Check if it's a cooking video without a recipe
                    has_cooking_tags = any(
                        'food' in tag.get('name', '').lower() or 
                        'cooking' in tag.get('name', '').lower() or
                        'recipe' in tag.get('name', '').lower()
                        for tag in video.get('tags', [])
                    )
                    
                    if has_cooking_tags and not video.get('recipe'):
                        cooking_video_without_recipe = video.get('id')
                        print(f"✅ Found cooking video without recipe: {video.get('title', 'Untitled')} (ID: {cooking_video_without_recipe})")
                        break
            
            if cooking_video_without_recipe:
                # Test manual recipe extraction
                extraction_response = await page.evaluate(f"""
                    fetch('{API_URL}/api/recipes/video/{cooking_video_without_recipe}/extract', {{
                        method: 'POST'
                    }})
                    .then(response => response.json())
                    .then(data => data)
                    .catch(error => {{ error: error.message }})
                """)
                
                if extraction_response.get('message'):
                    print(f"✅ Recipe extraction response: {extraction_response['message']}")
                    if extraction_response.get('recipe'):
                        recipe = extraction_response['recipe']
                        print(f"   📝 Extracted: {recipe.get('title', 'Untitled')}")
                        print(f"   🎯 Confidence: {recipe.get('extraction_confidence', 0):.2f}")
                else:
                    print(f"❌ Recipe extraction error: {extraction_response}")
            else:
                print("⚠️  No cooking videos without recipes found for testing extraction")
            
            # Test 5: Test recipe filtering
            print("\n📋 Test 5: Testing Recipe Filtering...")
            
            # Test cuisine filter
            cuisine_response = await page.evaluate(f"""
                fetch('{API_URL}/api/recipes/?cuisine=Italian')
                .then(response => response.json())
                .then(data => data)
                .catch(error => {{ error: error.message }})
            """)
            
            if isinstance(cuisine_response, list):
                print(f"✅ Cuisine filtering working - Found {len(cuisine_response)} Italian recipes")
            else:
                print(f"❌ Cuisine filtering error: {cuisine_response}")
            
            # Test difficulty filter
            difficulty_response = await page.evaluate(f"""
                fetch('{API_URL}/api/recipes/?difficulty=Easy')
                .then(response => response.json())
                .then(data => data)
                .catch(error => {{ error: error.message }})
            """)
            
            if isinstance(difficulty_response, list):
                print(f"✅ Difficulty filtering working - Found {len(difficulty_response)} easy recipes")
            else:
                print(f"❌ Difficulty filtering error: {difficulty_response}")
            
            # Test 6: Test recipe metadata endpoints
            print("\n📋 Test 6: Testing Recipe Metadata...")
            
            # Test cuisines list
            cuisines_response = await page.evaluate(f"""
                fetch('{API_URL}/api/recipes/cuisines/list')
                .then(response => response.json())
                .then(data => data)
                .catch(error => {{ error: error.message }})
            """)
            
            if isinstance(cuisines_response, list):
                print(f"✅ Cuisines list working - Found {len(cuisines_response)} cuisine types:")
                print(f"   🌍 {', '.join(cuisines_response[:5])}")
            else:
                print(f"❌ Cuisines list error: {cuisines_response}")
            
            # Test difficulties list
            difficulties_response = await page.evaluate(f"""
                fetch('{API_URL}/api/recipes/difficulties/list')
                .then(response => response.json())
                .then(data => data)
                .catch(error => {{ error: error.message }})
            """)
            
            if isinstance(difficulties_response, list):
                print(f"✅ Difficulties list working: {', '.join(difficulties_response)}")
            else:
                print(f"❌ Difficulties list error: {difficulties_response}")
            
            print("\n🎉 Recipe Feature Tests Completed!")
            print("\n📊 Summary:")
            print("   ✅ Recipe API endpoints working")
            print("   ✅ Video-recipe integration working")
            print("   ✅ Recipe search and filtering working")
            print("   ✅ Recipe extraction endpoint working")
            print("   ✅ Recipe metadata endpoints working")
            
            print("\n🔗 Available Recipe Features:")
            print("   • Recipe extraction from cooking videos")
            print("   • Structured ingredient and instruction storage")
            print("   • Recipe search by title, description, ingredients")
            print("   • Recipe filtering by cuisine and difficulty")
            print("   • Recipe export functionality")
            print("   • Multi-language support (Spanish/English)")
            
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(test_recipe_features())
